// Fill out your copyright notice in the Description page of Project Settings.


#include "SubSystems/UI/Widget/Components/VirtualLightSchematicView/Public/SLightHeightSchematicView.h"

#include "SlateOptMacros.h"

BEGIN_SLATE_FUNCTION_BUILD_OPTIMIZATION

SLightHeightSchematicView::SLightHeightSchematicView()
	: ViewType(ELightHeightSchematicViewType::VSVT_SpotLight)
	, BackgroundColor(FLinearColor::White)
	, ForegroundColor(FLinearColor::Yellow)
	, BackgroundThickness(4.0f)
	, ShapeSize(FVector2D::ZeroVector)
{
}

SLightHeightSchematicView::~SLightHeightSchematicView()
{
}

void SLightHeightSchematicView::Construct(const FArguments& InArgs)
{
	ViewType = InArgs._ViewType;
	BackgroundColor = InArgs._BackgroundColor;
	ForegroundColor = InArgs._ForegroundColor;
	BackgroundThickness = InArgs._BackgroundThickness;

	ShapeSize = InArgs._ShapeSize;

	CurrentHeight = InArgs._CurrentHeight;

	OnCurrentHeightChanged = InArgs._OnCurrentHeightChanged;
}

int32 SLightHeightSchematicView::OnPaint(const FPaintArgs& Args, const FGeometry& AllottedGeometry,
	const FSlateRect& MyCullingRect, FSlateWindowElementList& OutDrawElements, int32 LayerId,
	const FWidgetStyle& InWidgetStyle, bool bParentEnabled) const
{
	const bool bIsEnabled = ShouldBeEnabled(bParentEnabled);
	const ESlateDrawEffect DrawEffect = bIsEnabled ? ESlateDrawEffect::None : ESlateDrawEffect::DisabledEffect;

	FSlateRect DrawRect(FVector2D::ZeroVector, AllottedGeometry.GetLocalSize());

	DrawBackgroundLines(AllottedGeometry, DrawRect, OutDrawElements, LayerId, DrawEffect);
		
	if (!ShapeSize.IsZero())
	{
		DrawTargetShape(AllottedGeometry, InWidgetStyle, DrawRect, OutDrawElements, LayerId, DrawEffect);		
	}
	
	return LayerId;
}

FReply SLightHeightSchematicView::OnMouseButtonDown(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	FVector2D LocalSize = MyGeometry.GetLocalSize();
	if ((MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton) && !ShapeSize.IsZero() && !LocalSize.IsZero())
	{
		FVector2D MousePosInLocal = MyGeometry.AbsoluteToLocal(MouseEvent.GetScreenSpacePosition());
		FVector2D ShapeLoc(LocalSize.X * 0.5f, LocalSize.Y * (1.0f - CurrentHeight.Get()));
		
		bool bIsShapeUnderMouse = false;
		switch (ViewType)
		{
		case ELightHeightSchematicViewType::VSVT_SpotLight:
		case ELightHeightSchematicViewType::VSVT_RectLight:
			{
				FVector2D ShapeCenter = ShapeLoc + FVector2D(0, ShapeSize.Y * 0.5f);
				FVector2D MouseOffset = MousePosInLocal - ShapeCenter;
				bIsShapeUnderMouse = FMath::Abs(MouseOffset.X) < ShapeSize.X && FMath::Abs(MouseOffset.Y) < ShapeSize.Y;				
			}
			break;
		case ELightHeightSchematicViewType::VSVT_SphericalLight:
			{
				FVector2D MouseOffset = MousePosInLocal - ShapeLoc;
				bIsShapeUnderMouse = FMath::Abs(MouseOffset.X) < ShapeSize.X && FMath::Abs(MouseOffset.Y) < ShapeSize.X;
			}
			break;
		}

		if (bIsShapeUnderMouse)
		{
			return FReply::Handled().CaptureMouse(SharedThis(this));	
		}
	}

	return FReply::Unhandled();
}

FReply SLightHeightSchematicView::OnMouseButtonUp(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	if ((MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton) && HasMouseCaptureByUser(MouseEvent.GetUserIndex(), MouseEvent.GetPointerIndex()))
	{
		return FReply::Handled().ReleaseMouseCapture();	
	}

	return FReply::Unhandled();
}

FReply SLightHeightSchematicView::OnMouseMove(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	if (HasMouseCaptureByUser(MouseEvent.GetUserIndex(), MouseEvent.GetPointerIndex()))
	{
		FVector2D MousePosInLocal = MyGeometry.AbsoluteToLocal(MouseEvent.GetScreenSpacePosition());

		FVector2D LocalSize = MyGeometry.GetLocalSize();

		double FinalPosY = FMath::Clamp(MousePosInLocal.Y, 0, LocalSize.Y);

		float NewHeight = 1.0f - FinalPosY / LocalSize.Y;
		if (!CurrentHeight.IsBound())
		{
			CurrentHeight.Set(NewHeight);
		}

		Invalidate(EInvalidateWidgetReason::Paint);
		
		OnCurrentHeightChanged.ExecuteIfBound(NewHeight);
		
		return FReply::Handled();
	}

	return FReply::Unhandled();
}

FVector2D SLightHeightSchematicView::ComputeDesiredSize(float LayoutScaleMultiplier) const
{
	return ShapeSize;
}

void SLightHeightSchematicView::SetViewType(const ELightHeightSchematicViewType& InViewType)
{
	ViewType = InViewType;
	
	Invalidate(EInvalidateWidgetReason::Paint);
}

void SLightHeightSchematicView::SetBackgroundColor(const FLinearColor& InBackgroundColor)
{
	BackgroundColor = InBackgroundColor;
	Invalidate(EInvalidateWidgetReason::Paint);
}

void SLightHeightSchematicView::SetForegroundColor(const FLinearColor& InForegroundColor)
{
	ForegroundColor = InForegroundColor;
	Invalidate(EInvalidateWidgetReason::Paint);
}

void SLightHeightSchematicView::SetBackgroundThickness(float InBackgroundThickness)
{
	BackgroundThickness = InBackgroundThickness;

	Invalidate(EInvalidateWidgetReason::Paint);
}

void SLightHeightSchematicView::SetHandleSize(const FVector2D& InHandleSize)
{
	ShapeSize = InHandleSize;
	
	Invalidate(EInvalidateWidgetReason::Paint);
}

float SLightHeightSchematicView::GetCurrentHeight() const
{
	return CurrentHeight.Get();
}

void SLightHeightSchematicView::SetCurrentHeight(const TAttribute<float>& InCurrentHeight)
{
	CurrentHeight = InCurrentHeight;
	Invalidate(EInvalidateWidgetReason::Paint);
}

void SLightHeightSchematicView::DrawBackgroundLines(const FGeometry& AllottedGeometry, const FSlateRect& DrawRect,
                                                     FSlateWindowElementList& OutDrawElements, int32& LayerId, ESlateDrawEffect DrawEffect) const
{
	TArray<FVector2D> BackgroundLines;
	BackgroundLines.Add(DrawRect.GetTopLeft());
	BackgroundLines.Add(DrawRect.GetTopRight());

	FSlateDrawElement::MakeLines(OutDrawElements, LayerId, AllottedGeometry.ToPaintGeometry(), BackgroundLines, DrawEffect, BackgroundColor, true, BackgroundThickness);

	BackgroundLines.Empty();
	BackgroundLines.Add(DrawRect.GetBottomLeft());
	BackgroundLines.Add(DrawRect.GetBottomRight());
	
	FSlateDrawElement::MakeLines(OutDrawElements, LayerId, AllottedGeometry.ToPaintGeometry(), BackgroundLines, DrawEffect, BackgroundColor, true, BackgroundThickness);

	BackgroundLines.Empty();

	FVector2D BackgroundSize = DrawRect.GetSize();
	FVector2D BackgroundTopCenter = (DrawRect.GetTopLeft() + DrawRect.GetTopRight()) * 0.5f;
	BackgroundLines.Add(BackgroundTopCenter);
	BackgroundLines.Add(BackgroundTopCenter + FVector2D(0.0f, BackgroundSize.Y));

	FSlateDrawElement::MakeLines(OutDrawElements, LayerId, AllottedGeometry.ToPaintGeometry(), BackgroundLines, DrawEffect, BackgroundColor, true, BackgroundThickness * 0.5f);

	++LayerId;
}


void SLightHeightSchematicView::DrawTargetShape(const FGeometry& AllottedGeometry, const FWidgetStyle& InWidgetStyle, const FSlateRect& DrawRect,
                                                 FSlateWindowElementList& OutDrawElements, int32& LayerId, ESlateDrawEffect DrawEffect) const
{
	float CurrentHeightFactor = 1.0f - CurrentHeight.Get();
	FVector2f ShapeLoc(DrawRect.Right * 0.5f, DrawRect.Bottom * CurrentHeightFactor);

	TArray<FSlateVertex> VertexArray;
	TArray<SlateIndex> IndexArray;

	FSlateVertex TempVertex;
	TempVertex.Color = ForegroundColor.ToFColor(true);

	const FSlateRenderTransform& AccumulatedTransform = AllottedGeometry.ToPaintGeometry().GetAccumulatedRenderTransform();
	
	switch (ViewType)
	{
	case ELightHeightSchematicViewType::VSVT_SpotLight:
	case ELightHeightSchematicViewType::VSVT_RectLight:
		{
			TempVertex.SetPosition(AccumulatedTransform.TransformPoint(ShapeLoc + FVector2f(-ShapeSize.X * 0.5f, 0)));
			VertexArray.Add(TempVertex);

			TempVertex.SetPosition(AccumulatedTransform.TransformPoint(ShapeLoc + FVector2f(ShapeSize.X * 0.5f, 0)));
			VertexArray.Add(TempVertex);

			TempVertex.SetPosition(AccumulatedTransform.TransformPoint(ShapeLoc + FVector2f(ShapeSize.X * 0.5f, ShapeSize.Y)));
			VertexArray.Add(TempVertex);

			TempVertex.SetPosition(AccumulatedTransform.TransformPoint(ShapeLoc + FVector2f(-ShapeSize.X * 0.5f, ShapeSize.Y)));
			VertexArray.Add(TempVertex);

			IndexArray = { 0, 1, 2, 0, 2, 3};
		}
		break;
	case ELightHeightSchematicViewType::VSVT_SphericalLight:
		{
			TempVertex.Position = AccumulatedTransform.TransformPoint(ShapeLoc);
			VertexArray.Add(TempVertex);
			
			constexpr int32 NumSides = 360;
			constexpr float AngleStep = 2.0f * PI / NumSides;
			for (int32 Index = 0; Index < NumSides; ++Index)
			{
				const float Angle = AngleStep * Index;
				const FVector2f Offset = FVector2f(FMath::Cos(Angle), FMath::Sin(Angle)) * ShapeSize.X;

				TempVertex.TexCoords[0] = 0.5f + 0.5f * FMath::Cos(Angle);
				TempVertex.TexCoords[1] = 0.5f + 0.5f * FMath::Sin(Angle);
				
				TempVertex.Position = AccumulatedTransform.TransformPoint(ShapeLoc + Offset);
				VertexArray.Add(TempVertex);
			}

			for (int32 Index = 0; Index < NumSides; ++Index)
			{
				const int32 NextIndex = (Index + 1) % NumSides;
				IndexArray.Add(0);
				IndexArray.Add(Index + 1);
				IndexArray.Add(NextIndex + 1);
			}
		}
		break;
	}

	FSlateDrawElement::MakeCustomVerts(OutDrawElements, LayerId, FSlateResourceHandle(), VertexArray, IndexArray, nullptr, 0, 0, DrawEffect);

	++LayerId;
}

END_SLATE_FUNCTION_BUILD_OPTIMIZATION
