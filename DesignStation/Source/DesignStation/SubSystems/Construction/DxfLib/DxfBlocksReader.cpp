#include "DxfBlocksReader.h"
#include "pch.h"

#include "DxfTempData.h"
#include "src/dl_codes.h"
#include<locale>  
#include<codecvt>
#include "GeometryCalculator.h"

//DECLARE_LOG_CATEGORY_EXTERN(LogConstruction, Log, All)

using namespace DxfLib;

using module = DL_Codes::module;
using ObjectType = DL_Codes::ObjectType;

DxfLib::FDxfBlocksReader::FDxfBlocksReader()
{
    TargetModuletype = module::DXF_BLOCKS;
    BlockDataCache = nullptr;
    EntityCache = nullptr;
    MaxVertices = 0;
    VerTexCachesIndex = 0;
    VertexCaches.clear();
    EdgeType = -1;
}

DxfLib::FDxfBlocksReader::FDxfBlocksReader(const std::string& InSourceName) : SourceName(InSourceName)
{
    TargetModuletype = module::DXF_BLOCKS;
    BlockDataCache = nullptr;
    EntityCache = nullptr;
    MaxVertices = 0;
    VerTexCachesIndex = 0;
    VertexCaches.clear();
    EdgeType = -1;
}

DxfLib::FDxfBlocksReader::~FDxfBlocksReader()
{
    if (BlockDataCache)
    {
        delete BlockDataCache;
    }
    MaxVertices = 0;
    VerTexCachesIndex = 0;
    VertexCaches.clear();
    BlockDataCache = nullptr;
    EntityCache = nullptr;
}

void DxfLib::FDxfBlocksReader::processCodeValuePair(unsigned int NewGroupCode, const std::string& NewGroupValue)
{

    DL_DxfReader::processCodeValuePair(NewGroupCode, NewGroupValue);

    if (Moduletype != module::DXF_BLOCKS)
    {
        return;
    }

    //直接过滤 handle和Ownerhandle 写文件时重新分配
    if (NewGroupCode == 5 || NewGroupCode == 330)
    {
        return;
    }

    if (NewGroupCode == DL_ENTITY_TYPE_CODE) {
        // Add the previously parsed entity via creationInterface

        GenerateAttributes();
        switch (currentObjectType) {
        case ObjectType::DXF_BLOCK:
        {
            //一个Block起始 
            StartBlock();
        }
        break;
        case ObjectType::DXF_ENTITY_POINT:
        {
            AddPoint();
        }
        break;
        case ObjectType::DXF_ENTITY_LINE:
        {
            AddLine();
        }
        break;
        case ObjectType::DXF_ENTITY_LWPOLYLINE:
        {
            AddLWPolyLine();
        }
        break;
        case ObjectType::DXF_ENTITY_ARC:
        {
            AddArc();
        }
        break;
        case ObjectType::DXF_ENTITY_CIRCLE:
        {
            AddCircle();
        }
        break;
        case ObjectType::DXF_ENTITY_ATTDEF:
        {
            AddAttDef();
        }
        break;
        case ObjectType::DXF_ENTITY_TEXT:
        {
            AddText();
        }
        break;
        case ObjectType::DXF_ENTITY_MTEXT:
            AddMText();
            break;
        case ObjectType::DXF_ENTITY_SPLINE:
            AddSpline();
            break;
        case ObjectType::DXF_ENDBLK:
        {
            EndBlock();
            //一个Blcok结束
        }
        break;
        case ObjectType::DXF_ENTITY_HATCH:
        {
            AddHatchData();
        }
        break;
        default:
            break;
        }
        values.clear();
        // Read Blocks:
        if (NewGroupValue == DXF_VALUE_BLOCK) {
            currentObjectType = ObjectType::DXF_BLOCK;
        }
        else if (NewGroupValue == DXF_VALUE_ENDBLK) {
            currentObjectType = ObjectType::DXF_ENDBLK;
        }
        // Read entities:
        else if (NewGroupValue._Equal(DXF_VALUE_ATTDEF))
        {
            currentObjectType = ObjectType::DXF_ENTITY_ATTDEF;
        }
        else if (NewGroupValue == DXF_VALUE_POINT) {
            currentObjectType = ObjectType::DXF_ENTITY_POINT;
        }
        else if (NewGroupValue == DXF_VALUE_LINE) {
            currentObjectType = ObjectType::DXF_ENTITY_LINE;
        }
        else if (NewGroupValue == DXF_VALUE_XLINE) {
            currentObjectType = ObjectType::DXF_ENTITY_XLINE;
        }
        else if (NewGroupValue == DXF_VALUE_RAY) {
            currentObjectType = ObjectType::DXF_ENTITY_RAY;
        }
        else if (NewGroupValue == DXF_VALUE_POLYLINE) {
            currentObjectType = ObjectType::DXF_ENTITY_POLYLINE;
        }
        else if (NewGroupValue == DXF_VALUE_LWPOLYLINE) {
            currentObjectType = ObjectType::DXF_ENTITY_LWPOLYLINE;
        }
        else if (NewGroupValue == "VERTEX") {
            currentObjectType = ObjectType::DXF_ENTITY_VERTEX;
        }
        else if (NewGroupValue == "SPLINE") {
            currentObjectType = ObjectType::DXF_ENTITY_SPLINE;
        }
        else if (NewGroupValue == DXF_VALUE_ARC) {
            currentObjectType = ObjectType::DXF_ENTITY_ARC;
        }
        else if (NewGroupValue == "ELLIPSE") {
            currentObjectType = ObjectType::DXF_ENTITY_ELLIPSE;
        }
        else if (NewGroupValue == DXF_VALUE_CIRCLE) {
            currentObjectType = ObjectType::DXF_ENTITY_CIRCLE;
        }
        else if (NewGroupValue == DXF_VALUE_TEXT) {
            currentObjectType = ObjectType::DXF_ENTITY_TEXT;
        }
        else if (NewGroupValue == DXF_Value_MTEXT) {
            currentObjectType = ObjectType::DXF_ENTITY_MTEXT;
        }
        else if (NewGroupValue == "ARCALIGNEDTEXT") {
            currentObjectType = ObjectType::DXF_ENTITY_ARCALIGNEDTEXT;
        }
        else if (NewGroupValue == "DIMENSION") {
            currentObjectType = ObjectType::DXF_ENTITY_DIMENSION;
        }
        else if (NewGroupValue == "LEADER") {
            currentObjectType = ObjectType::DXF_ENTITY_LEADER;
        }
        else if (NewGroupValue == "HATCH") {
            currentObjectType = ObjectType::DXF_ENTITY_HATCH;
        }
        else if (NewGroupValue == "IMAGE") {
            currentObjectType = ObjectType::DXF_ENTITY_IMAGE;
        }
        else if (NewGroupValue == "IMAGEDEF") {
            currentObjectType = ObjectType::DXF_ENTITY_IMAGEDEF;
        }
        else if (NewGroupValue == "TRACE") {
            currentObjectType = ObjectType::DXF_ENTITY_TRACE;
        }
        else if (NewGroupValue == "SOLID") {
            currentObjectType = ObjectType::DXF_ENTITY_SOLID;
        }
        else if (NewGroupValue == "3DFACE") {
            currentObjectType = ObjectType::DXF_ENTITY_3DFACE;
        }
        else if (NewGroupValue == DXF_VLAUE_SEQEND) {
            currentObjectType = ObjectType::DXF_ENTITY_SEQEND;
        }
        else if (NewGroupValue == "XRECORD") {
            currentObjectType = ObjectType::DXF_XRECORD;
        }
        else {
            currentObjectType = ObjectType::DXF_UNKNOWN;
        }

    }
    bool bHandled = false;
    if (NewGroupCode < DL_DXF_MAXGROUPCODE)
    {
        //如果是多段线需要对顶点数据单独缓存
        if (currentObjectType == ObjectType::DXF_ENTITY_LWPOLYLINE)
        {
            bHandled= HandleLWPolylineData();
        }
        else if (currentObjectType == ObjectType::DXF_ENTITY_HATCH)
        {
            bHandled =HandleHatchData();
        }
        else if (currentObjectType == ObjectType::DXF_ENTITY_SPLINE)
        {
            bHandled = HandleSplineData();
        }
    }
    if (!bHandled)
    {
        values[NewGroupCode] = NewGroupValue;
    }
    return;
}

/*
bool DxfLib::FDxfBlocksReader::LoadFile(const std::string& file)
{
    //1.获取不带路径的文件名
    std::string::size_type iPos = file.find_last_of('\\') + 1;
    std::string filename = file.substr(iPos, file.length() - iPos);
    //2.获取不带后缀的文件名
    SourceName = filename.substr(0, filename.rfind("."));

    DL_DxfReader::LoadFile(file);
    return false;
}
*/



void DxfLib::FDxfBlocksReader::StartBlock()
{

    //正常情况BlockDataCache在读取到Block字段是为null，如果不是清空当前内存,防止泄露
    if (BlockDataCache)
    {
        delete BlockDataCache;
    }

    std::string name = getStringValue(2, "");
    std::string BlockSourceName = SourceName;
    if (name._Equal("*Model_Space") || name._Equal("*Paper_Space") || name._Equal("*Paper_Space0"))
    {
        BlockSourceName = "";
    }

    BlockDataCache = new FDxfBlockData(
        name,
        FVector(
            getRealValue(10, 0.0),
            getRealValue(20, 0.0),
            getRealValue(30, 0.0)),
        attributes,
        BlockSourceName
    );
    //Block头标记，开启一个Block 缓存，直到遇到EndBlock结束
}

void DxfLib::FDxfBlocksReader::EndBlock()
{

    //当读取到EndBlock时，一个Block结束
    if (BlockDataCache)
    {
		//UE_LOG(LogConstruction, Log, TEXT("ParseBlock:%s"), *FString(BlockDataCache->Name.c_str()));
        FDxfTempData::GetInstance()->GetDxfBlocks()->AddBlock(BlockDataCache);
        BlockDataCache = nullptr;
    }
}

void DxfLib::FDxfBlocksReader::AddAttDef()
{
    if (!BlockDataCache)
    {
        return;
    }
    std::string tag = getStringValue(2, "");
    if (tag.empty())
    {
        return;
    }
    // flags
    //getIntValue(DL_ATT_FLAGS_CODE, 0);
    //getRealValue(DL_TXTHI_CODE, 0.0);
    FDxfAttDefData* AttDefData = new FDxfAttDefData(
        FVector(getRealValue(10, 0.0), getRealValue(20, 0.0), getRealValue(30, 0.0)),
        FVector(getRealValue(11, 0.0), getRealValue(21, 0.0), getRealValue(31, 0.0)),
        //height
        getRealValue(40, 2.5),
        // x scale
        getRealValue(41, 1.0),
        // generation flags
        getIntValue(71, 0),
        // h just
        getIntValue(72, 0),
        // v just
        getIntValue(73, 0),
        // TextAngle
        getRealValue(50, 0.0),
        // text
        getStringValue(1, ""),
        // style
        getStringValue(7, ""),
        //tag
        tag,
        attributes);
    BlockDataCache->AddAttDef(AttDefData);
}

void DxfLib::FDxfBlocksReader::AddArc()
{
    if (!BlockDataCache)
    {
        return;
    }
    FDxfArcData* ArcData = new FDxfArcData(
        FVector(getRealValue(10, 0), getRealValue(20, 0), getRealValue(30, 0)),
        getRealValue(40, 0.0),
        getRealValue(50, 0.0),
        getRealValue(51, 0.0),
        attributes);
    BlockDataCache->AddChildEntity(ArcData);
}

void DxfLib::FDxfBlocksReader::AddCircle()
{
    if (!BlockDataCache)
    {
        return;
    }
    FDxfCircleData* CircleData = new FDxfCircleData(
        FVector(getRealValue(10, 0), getRealValue(20, 0), getRealValue(30, 0)),
        getRealValue(40, 0),
        attributes);
    BlockDataCache->AddChildEntity(CircleData);
}

void DxfLib::FDxfBlocksReader::AddPoint()
{
    if (!BlockDataCache)
    {
        return;
    }
    FDxfPointData* PointData = new FDxfPointData(
        FVector(getRealValue(10, 0), getRealValue(20, 0), getRealValue(30, 0)),
        attributes);
    BlockDataCache->AddChildEntity(PointData);
}

void DxfLib::FDxfBlocksReader::AddLine()
{
    if (!BlockDataCache)
    {
        return;
    }
    FDxfLineData* LineData = new FDxfLineData(
        FVector(getRealValue(10, 0), getRealValue(20, 0), getRealValue(30, 0)),
        FVector(getRealValue(11, 0), getRealValue(21, 0), getRealValue(31, 0)),
        attributes);
    BlockDataCache->AddChildEntity(LineData);
}

void DxfLib::FDxfBlocksReader::AddLWPolyLine()
{
    if (!BlockDataCache)
    {
        return;
    }
    uint32 VertexSize = std::min(MaxVertices, (uint32)VertexCaches.size());
    FDxfLWPolyLineData* LWPolyLineData = new FDxfLWPolyLineData(
        MaxVertices,
        getIntValue(70, 0),
        attributes
    );
    for (size_t i = 0; i < VertexCaches.size(); i++)
    {
        LWPolyLineData->AddPoint(VertexCaches[i]);
    }
    BlockDataCache->AddChildEntity(LWPolyLineData);
}

void DxfLib::FDxfBlocksReader::AddSpline()
{
    if (!BlockDataCache|| !EntityCache)
    {
        return;
    }
    FDxfSplineData* SplineData = (FDxfSplineData*)(EntityCache);
    if (!SplineData)
    {
        return;
    }
    SplineData->Normal = FVector(
        getRealValue(210, 0),
        getRealValue(220, 0),
        getRealValue(230, 0));
    SplineData->flags = getIntValue(70, 0);
    SplineData->degree = getIntValue(71, 0);
    SplineData->nKnots = getIntValue(72, 0);
    SplineData->nControl = getIntValue(73, 0);
    SplineData->nFit = getIntValue(74, 0);

    SplineData->tangentStart = FVector(
        getRealValue(12, 0),
        getRealValue(22, 0),
        getRealValue(32, 0)
    );
    SplineData->tangentEnd = FVector(
        getRealValue(13, 0),
        getRealValue(23, 0),
        getRealValue(33, 0)
    );

    BlockDataCache->AddChildEntity(SplineData);
    EntityCache = nullptr;
}

bool DxfLib::FDxfBlocksReader::HandleSplineData()
{
    if (!EntityCache)
    {
        EntityCache = new FDxfSplineData(attributes);
    }
    FDxfSplineData* SplineData = (FDxfSplineData*)(EntityCache);

    if (!SplineData)
    {
        return false;
    }

    if (groupCode == 40)//Knot
    {
        SplineData->Knots.push_back(toReal(groupValue));
    }
    else if(groupCode == 10)//ControlPoint
    {
        SplineData->ControlPoints.push_back(FVector(toReal(groupValue), 0.f, 0.f));
    }
    else if (groupCode == 20)//ControlPoint
    {
        SplineData->ControlPoints.back().Y = toReal(groupValue);
    }
    else if(groupCode == 30)
    {
        SplineData->ControlPoints.back().Z = toReal(groupValue);
    }
    else if(groupCode == 11)//FitPoint
    { 
        SplineData->FitPointData.push_back(FVector(toReal(groupValue), 0.f, 0.f));
    }
    else if (groupCode == 21)
    {
        SplineData->FitPointData.back().Y = toReal(groupValue);
    }
    else if (groupCode == 31)
    {
        SplineData->FitPointData.back().Z = toReal(groupValue);
    }
    else
    {
        return false;
    }
    return true;
}

void DxfLib::FDxfBlocksReader::AddText()
{
    if (!BlockDataCache)
    {
        return;
    }
    // flags
    //getIntValue(DL_ATT_FLAGS_CODE, 0);
    //getRealValue(DL_TXTHI_CODE, 0.0);
    FDxfTextData* TextData = new FDxfTextData(
        FVector(getRealValue(10, 0.0), getRealValue(20, 0.0), getRealValue(30, 0.0)),
        FVector(getRealValue(11, 0.0), getRealValue(21, 0.0), getRealValue(31, 0.0)),
        //height
        getRealValue(40, 2.5),
        // x scale
        getRealValue(41, 1.0),
        // generation flags
        getIntValue(71, 0),
        // h just
        getIntValue(72, 0),
        // v just
        getIntValue(73, 0),
        // TextAngle
        getRealValue(50, 0.0),
        // text
        getStringValue(1, ""),
        // style
        getStringValue(7, ""),
        attributes);

    BlockDataCache->AddChildEntity(TextData);
}

void DxfLib::FDxfBlocksReader::AddMText()
{
    if (!BlockDataCache)
    {
        return;
    }
    double angle = 0.0;
    if (hasValue(50)) {
       angle = getRealValue(50, 0.0);
    }
    else if (hasValue(11) && hasValue(21)) {
        double x = getRealValue(11, 0.0);
        double y = getRealValue(21, 0.0);

        if (fabs(x) < 1.0e-6) {
            if (y > 0.0) {
                angle = M_PI / 2.0;
            }
            else {
                angle = M_PI / 2.0 * 3.0;
            }
        }
        else {
            angle = atan(y / x);
        }
    }
    FDxfMTextData* MText = new FDxfMTextData(
        FVector(getRealValue(10,0.0),getRealValue(20,0.0),getRealValue(30,0.0)),
        FVector(getRealValue(11, 0.0), getRealValue(21, 0.0), getRealValue(31, 0.0)),
        getRealValue(40, 2.5),
        // width
        getRealValue(41, 0.0),
        // attachment point
        getIntValue(71, 1),
        // drawing direction
        getIntValue(72, 1),
        // line spacing style
        getIntValue(73, 1),
        // line spacing factor
        getRealValue(44, 1.0),
        // text
        getStringValue(1, ""),
        // style
        getStringValue(7, ""),
        angle,
        attributes
    );
    BlockDataCache->AddChildEntity(MText);
}

bool DxfLib::FDxfBlocksReader::HandleLWPolylineData()
{
    if (groupCode == 90) {
        MaxVertices = toInt(groupValue);
        if (MaxVertices > 0) {

            VertexCaches.clear();
            VertexCaches.resize(MaxVertices);
        }
        VerTexCachesIndex = -1;
        return true;
    }
    else if (groupCode == 10)
    {
        VerTexCachesIndex++;
        VerTexCachesIndex = std::min(VerTexCachesIndex, (int)VertexCaches.size());
        VertexCaches[VerTexCachesIndex].X = toReal(groupValue);
        return true;

    }
    else if (groupCode == 20)
    {
        VertexCaches[VerTexCachesIndex].Y = toReal(groupValue);
        return true;
    }
    return false;
}

void DxfLib::FDxfBlocksReader::HandleDIMENSION()
{
    int DimType = getIntValue(70, 0) & 0x07;

    switch (DimType)
    {
    case 0:
        AddDimLinear();
        break;
    case 1:
        AddDimAligned();
        break;
    case 2:
        AddDimAngular();
        break;
    case 3:
        AddDimDiametric();
        break;
    case 4:
        AddDimRadial();
        break;
    case 5:
        AddDimAngular3P();
        break;
    case 6:
        AddDimOrdinate();
        break;
    default:
        break;
    }

}

FDxfDimensionBase DxfLib::FDxfBlocksReader::GetDimData()
{
    return FDxfDimensionBase(
        // def point
        FVector(getRealValue(10, 0.0), getRealValue(20, 0.0), getRealValue(30, 0.0)),
        // text middle point
        FVector(getRealValue(11, 0.0), getRealValue(21, 0.0), getRealValue(31, 0.0)),
        // type
        getIntValue(70, 0),
        // attachment point
        getIntValue(71, 5),
        // line sp. style
        getIntValue(72, 1),
        // line sp. factor
        getRealValue(41, 1.0),
        // text
        getStringValue(1, ""),
        // style
        getStringValue(3, ""),
        // angle
        getRealValue(53, 0.0)
    );
}

void DxfLib::FDxfBlocksReader::AddDimLinear()
{
    if (!BlockDataCache)
    {
        return;
    }
    GenerateAttributes();
    FDxfDimensionBase DimBase = GetDimData();

    FDxfDimLinearData* DimLinear = new FDxfDimLinearData(
        FVector(getRealValue(13, 0.0), getRealValue(23, 0.0), getRealValue(33, 0.0)),
        FVector(getRealValue(14, 0.0), getRealValue(24, 0.0), getRealValue(34, 0.0)),
        getRealValue(50, 0.0),
        getRealValue(52, 0.0),
        DimBase,
        attributes);

    BlockDataCache->AddChildEntity(DimLinear);

}

void DxfLib::FDxfBlocksReader::AddDimAligned()
{
    if (!BlockDataCache)
    {
        return;
    }
    GenerateAttributes();
    FDxfDimensionBase DimBase = GetDimData();
    FDxfDimAlignedData* AlignedData = new FDxfDimAlignedData(
        FVector(getRealValue(13, 0.0), getRealValue(23, 0.0), getRealValue(33, 0.0)),
        FVector(getRealValue(14, 0.0), getRealValue(24, 0.0), getRealValue(34, 0.0)),
        DimBase,
        attributes
    );
    BlockDataCache->AddChildEntity(AlignedData);
}

void DxfLib::FDxfBlocksReader::AddDimRadial()
{
    if (!BlockDataCache)
    {
        return;
    }
    GenerateAttributes();
    FDxfDimensionBase DimBase = GetDimData();
    FDxfDimRadialData* RadialData = new FDxfDimRadialData(
        FVector(getRealValue(15, 0.0), getRealValue(25, 0.0), getRealValue(35, 0.0)),
        getRealValue(40, 0.0),
        DimBase,
        attributes
    );
    BlockDataCache->AddChildEntity(RadialData);
}

void DxfLib::FDxfBlocksReader::AddDimDiametric()
{
    if (!BlockDataCache)
    {
        return;
    }
    GenerateAttributes();
    FDxfDimensionBase DimBase = GetDimData();
    FDxfDimDiametricData* DiametricData = new FDxfDimDiametricData(
        FVector(getRealValue(15, 0.0), getRealValue(25, 0.0), getRealValue(35, 0.0)),
        getRealValue(40, 0.0),
        DimBase,
        attributes
    );
    BlockDataCache->AddChildEntity(DiametricData);
}

void DxfLib::FDxfBlocksReader::AddDimAngular()
{
    if (!BlockDataCache)
    {
        return;
    }
    GenerateAttributes();
    FDxfDimensionBase DimBase = GetDimData();

    FDxfDimAngular2LineData* Angular2LineData = new FDxfDimAngular2LineData(
        FVector(getRealValue(13, 0.0), getRealValue(23, 0.0), getRealValue(33, 0.0)),
        FVector(getRealValue(14, 0.0), getRealValue(24, 0.0), getRealValue(34, 0.0)),
        FVector(getRealValue(15, 0.0), getRealValue(25, 0.0), getRealValue(35, 0.0)),
        FVector(getRealValue(16, 0.0), getRealValue(26, 0.0), getRealValue(36, 0.0)),
        DimBase,
        attributes
    );
    BlockDataCache->AddChildEntity(Angular2LineData);
}

void DxfLib::FDxfBlocksReader::AddDimAngular3P()
{
    if (!BlockDataCache)
    {
        return;
    }
    GenerateAttributes();
    FDxfDimensionBase DimBase = GetDimData();
    FDxfDimAngular3PointData* Angular3Point = new FDxfDimAngular3PointData(
        FVector(getRealValue(13, 0.0), getRealValue(23, 0.0), getRealValue(33, 0.0)),
        FVector(getRealValue(14, 0.0), getRealValue(24, 0.0), getRealValue(34, 0.0)),
        FVector(getRealValue(15, 0.0), getRealValue(25, 0.0), getRealValue(35, 0.0)),
        DimBase,
        attributes
    );

    BlockDataCache->AddChildEntity(Angular3Point);
}

void DxfLib::FDxfBlocksReader::AddDimOrdinate()
{
    if (!BlockDataCache)
    {
        return;
    }
    GenerateAttributes();
    FDxfDimensionBase DimBase = GetDimData();

    FDxfDimOrdinateData* OrdinateData = new FDxfDimOrdinateData(
        FVector(getRealValue(13, 0.0), getRealValue(23, 0.0), getRealValue(33, 0.0)),
        FVector(getRealValue(14, 0.0), getRealValue(24, 0.0), getRealValue(34, 0.0)),
        (getIntValue(70,0)&0x40) == 0x40,
        DimBase,
        attributes
    );
    BlockDataCache->AddChildEntity(OrdinateData);
}

bool DxfLib::FDxfBlocksReader::HandleHatchData()
{
    if (!EntityCache)
    {
        EntityCache = new FDxfHatchData(FDxfHatchPatternData(),attributes);
    }
    FDxfHatchData* HatchDataCache = static_cast<FDxfHatchData*>(EntityCache);
    if (!HatchDataCache)
    {
        return true;
    }
    std::shared_ptr<FDxfHatchLoopEdgeData> LastLoopEdges = nullptr;
    if (!HatchDataCache->LoopEdges.empty())
    {
        LastLoopEdges = HatchDataCache->LoopEdges.back();
    }

    //loopEdgedata
    if (groupCode == 92)
    {
        //NewLoop
        LastLoopEdges = std::make_shared<FDxfHatchLoopEdgeData>(toInt(groupValue));
        HatchDataCache->LoopEdges.push_back(LastLoopEdges);
        return true;
    }


    
    if (!LastLoopEdges)
    {
        return false;
    }
    
    if (groupCode == 98)
    {
        HatchDataCache->SeedCount = toInt(groupValue);
    }
    if (groupCode == 10&& HatchDataCache->SeedCount != 0)
    {

        HatchDataCache->Seed.X = toReal(groupValue);
        return true;
    }
    else if (groupCode == 20 && HatchDataCache->SeedCount != 0)
    {
        HatchDataCache->Seed.Y = toReal(groupValue);
        return true;
    }

    std::shared_ptr<FDxfHatchEdgeData> LastEdge = nullptr;
    if (!LastLoopEdges->Edges.empty()) LastEdge = LastLoopEdges->Edges.back();

	//PolyLineEdge
    if (LastLoopEdges->IsPolyline())
    {

		std::shared_ptr<FDxfHatchPolyLineEdgeData>PolyLineEdge = nullptr;
        if (!LastEdge)
        {
            PolyLineEdge = std::make_shared<FDxfHatchPolyLineEdgeData>();
            LastLoopEdges->Edges.push_back(PolyLineEdge);
        }
        else if(groupCode == 72)
        {
            PolyLineEdge = std::make_shared<FDxfHatchPolyLineEdgeData>();
            LastLoopEdges->Edges.push_back(PolyLineEdge);
        }
        else
        {
			PolyLineEdge = std::dynamic_pointer_cast<FDxfHatchPolyLineEdgeData>(LastEdge);
        }
        if (groupCode == 72)
        {
            PolyLineEdge->bConvexity = toInt(groupValue); 
        }
        else if (groupCode == 73)
        {
            PolyLineEdge->bClose = toInt(groupValue);
        }
        else if (groupCode == 10)
        {
            PolyLineEdge->AddPoints(FVector(toReal(groupValue), 0, 0));
        }
        else if (groupCode == 20)
        {
            PolyLineEdge->Points.back().Y = toReal(groupValue);
        }
        else if (groupCode == 42)
        {
            PolyLineEdge->Points.back().Z = toReal(groupValue);
        }
        else
        {
            return false;
        }
        return true;
    }
    else
    {
        if (groupCode == 72)
        {
            int edgeType = toInt(groupValue);
            switch (edgeType)
            {
            case 1: // 直线
                LastEdge = std::make_shared<FDxfHatchLineEdgeData>();
                LastLoopEdges->Edges.push_back(LastEdge);
                break;
            case 2: // 圆弧
                LastEdge = std::make_shared<FDxfHatchCircularArcEdgeData>();
                LastLoopEdges->Edges.push_back(LastEdge);
                break;
            case 3: // 椭圆弧
                LastEdge = std::make_shared<FDxfHatchEllipticalArcEdgeData>();
                LastLoopEdges->Edges.push_back(LastEdge);
                break;
            case 4: // 样条
                LastEdge = std::make_shared<FDxfHatchSplineEdgeData>();
                LastLoopEdges->Edges.push_back(LastEdge);
                break;
            default:
                return false;
            }
            return true;
        }

        if (!LastEdge)
        {
            return false;
        }

        // 解析具体边数据
        switch (LastEdge->PathType)
        {
        case 1: // 直线
        {
            auto LineEdge = std::dynamic_pointer_cast<FDxfHatchLineEdgeData>(LastEdge);
            if (!LineEdge) return false;
            if (groupCode == 10)
            {
                LineEdge->StartPoint.X = toReal(groupValue);
                return true;
            }
            else if (groupCode == 20)
            {
                LineEdge->StartPoint.Y = toReal(groupValue);
                return true;
            }
            else if (groupCode == 11)
            {
                LineEdge->EndPoint.X = toReal(groupValue);
                return true;
            }
            else if (groupCode == 21)
            {
                LineEdge->EndPoint.Y = toReal(groupValue);
                return true;
            }
        }
        break;
        case 2: // 圆弧
        {
            auto ArcEdge = std::dynamic_pointer_cast<FDxfHatchCircularArcEdgeData>(LastEdge);
            if (!ArcEdge) return false;
            if (groupCode == 10)
            {
                ArcEdge->Center.X = toReal(groupValue);
                return true;
            }
            else if (groupCode == 20)
            {
                ArcEdge->Center.Y = toReal(groupValue);
                return true;
            }
            else if (groupCode == 40)
            {
                ArcEdge->Radius = toReal(groupValue);
                return true;
            }
            else if (groupCode == 50)
            {
                ArcEdge->StartAngle = toReal(groupValue);
                return true;
            }
            else if (groupCode == 51)
            {
                ArcEdge->EndAngle = toReal(groupValue);
                return true;
            }
            else if (groupCode == 73)
            {
                ArcEdge->bCounterClockWise = toInt(groupValue) != 0;
                return true;
            }
        }
        break;
        case 3: // 椭圆弧
        {
            auto EllipseEdge = std::dynamic_pointer_cast<FDxfHatchEllipticalArcEdgeData>(LastEdge);
            if (!EllipseEdge) return false;
            if (groupCode == 10)
            {
                EllipseEdge->Center.X = toReal(groupValue);
                return true;
            }
            else if (groupCode == 20)
            {
                EllipseEdge->Center.Y = toReal(groupValue);
                return true;
            }
            else if (groupCode == 11)
            {
                EllipseEdge->MajorAxisEnd.X = toReal(groupValue);
                return true;
            }
            else if (groupCode == 21)
            {
                EllipseEdge->MajorAxisEnd.Y = toReal(groupValue);
                return true;
            }
            else if (groupCode == 40)
            {
                EllipseEdge->MinorToMajorRatio = toReal(groupValue);
                return true;
            }
            else if (groupCode == 50)
            {
                EllipseEdge->StartAngle = toReal(groupValue);
                return true;
            }
            else if (groupCode == 51)
            {
                EllipseEdge->EndAngle = toReal(groupValue);
                return true;
            }
            else if (groupCode == 73)
            {
                EllipseEdge->bCounterClockWise = toInt(groupValue) != 0;
                return true;
            }
        }
        break;
        case 4: // 样条
        {
            auto SplineEdge = std::dynamic_pointer_cast<FDxfHatchSplineEdgeData>(LastEdge);
            if (!SplineEdge) return false;
            if (groupCode == 94)
            {
                SplineEdge->Degree = toInt(groupValue);
                return true;
            }
            if (groupCode == 73)
            {
                SplineEdge->Rational = toInt(groupValue);
                return true;
            }
            if (groupCode == 74)
            {
                SplineEdge->Cycle = toInt(groupValue);
                return true;
            }
            else if (groupCode == 95)
            {
                SplineEdge->nKnots = toInt(groupValue);
                return true;
            }
            else if (groupCode == 96)
            {
                SplineEdge->nControl = toInt(groupValue);
                return true;
            }
            else if (groupCode == 97)
            {
                SplineEdge->nFit = toInt(groupValue);
                return true;
            }
            else if (groupCode == 40)
            {
                // Knot value
                 SplineEdge->Knots.push_back(toReal(groupValue)); // 若有Knots成员
                return true;
            }
            else if (groupCode == 10)
            {
                SplineEdge->ControlPoints.push_back(FVector(toReal(groupValue), 0, 0));
                return true;
            }
            else if (groupCode == 20)
            {
                if (!SplineEdge->ControlPoints.empty())
                    SplineEdge->ControlPoints.back().Y = toReal(groupValue);
                return true;
            }
            else if (groupCode == 11)
            {
                SplineEdge->FitPointData.push_back(FVector(toReal(groupValue), 0, 0));
                return true;
            }
            else if (groupCode == 21)
            {
                if (!SplineEdge->FitPointData.empty())
                    SplineEdge->FitPointData.back().Z = toReal(groupValue);
                return true;
            }
        }
        break;
        default:
            break;
        }

    }
    if (groupCode == 97)
    {
        return true;
    }
    return false;
}

void DxfLib::FDxfBlocksReader::AddHatchData()
{
    if (!BlockDataCache|| !EntityCache)
    {
        return;
    }
	FDxfHatchData* HatchDataCache = (FDxfHatchData*)(EntityCache);
    std::string PatternName =  getStringValue(2, "");

    if (!HatchDataCache)
    {
        return;
    }
    if (PatternName.empty())
    {
        return;
    }
    HatchDataCache->bSolid = getIntValue(70, 0);
    HatchDataCache->HatchStyle = getIntValue(75, 0);
    HatchDataCache->HatchType = getIntValue(76, 1);
    
    HatchDataCache->HatchPattern.Angle = getRealValue(52, 0.0);
    HatchDataCache->HatchPattern.Scale = getRealValue(41, 0.0);

    HatchDataCache->HatchPattern.Name = PatternName;
    
    BlockDataCache->AddChildEntity(HatchDataCache);
    EntityCache = nullptr;
}
